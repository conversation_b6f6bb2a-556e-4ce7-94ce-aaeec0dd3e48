<html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
        <title>8D Audio Converter</title>
        <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css" integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T" crossorigin="anonymous">
        <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='upload.css') }}" />
        <script src="https://code.jquery.com/jquery-3.3.1.slim.min.js" integrity="sha384-q8i/X+965DzO0rT7abK41JStQIAqVgRVzpbzo5smXKp4YfRvH+8abtTE1Pi6jizo" crossorigin="anonymous" defer></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js" integrity="sha384-UO2eT0CpHqdSJQ6hJty5KVphtPhzWj9WO1clHTMGa3JDZwrnQq4sF86dIHNDz0W1" crossorigin="anonymous" defer></script>
        <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js" integrity="sha384-JjSmVgyd0p3pXB1rRibZUAYoIIy6OrQ6VrjIEaFf/nJGzIxFDsf4x0xIM+B07jRM" crossorigin="anonymous" defer></script>
    </head>
    <body>
        <div class="jumbotron" id="title">
            <h2>8D Audio Converter</h2>
        </div>
        <div id="form">
            <form action="/uploader" method="POST" enctype="multipart/form-data">
                <input type="file" class="btn btn-outline-info" name="file" value="Upload mp3" />
                <br> <br>
                <p>
                    Period (200 recommended): <input type="number" name="period" class="box" />
                    <br> <br>
                    Output name: <input type="text" name="outputname" class="box" />
                </p>
                <input type="submit" onclick="converting()" class="btn btn-outline-info" value="Convert" id="submit"/>
                <p style="visibility:hidden" id="loading">
                  Your file is being converted and will be available for download soon...
                </p>
                <script>
                    function converting() {
                        document.getElementById("submit").style.visibility = 'hidden';
                        document.getElementById("loading").style.visibility = 'visible';
                    }
                </script>
            </form>
        </div>
    </body>
</html>
